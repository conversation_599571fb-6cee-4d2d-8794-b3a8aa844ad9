server:
  port: "8080"
  mode: "debug"

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "gitops_platform"
  sslmode: "disable"

argocd:
  server: "localhost:8080"
  username: "admin"
  password: "admin"
  token: ""
  insecure: true

auth:
  jwt_secret: "your-super-secret-jwt-key-change-this-in-production"
  token_expiry: 3600    # 1 hour
  refresh_expiry: 86400 # 24 hours

log:
  level: "info"
  format: "json"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
