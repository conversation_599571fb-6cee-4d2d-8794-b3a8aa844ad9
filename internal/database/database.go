package database

import (
	"fmt"
	"gitops-platform/internal/config"
	"gitops-platform/internal/models"
	"gitops-platform/pkg/logger"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

func Initialize(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s",
		cfg.Host, cfg.User, cfg.Password, cfg.DBName, cfg.Port, cfg.SSLMode)

	// Configure GORM logger
	var gormLogLevel gormLogger.LogLevel
	switch logger.GetLevel() {
	case "debug":
		gormLogLevel = gormLogger.Info
	case "info":
		gormLogLevel = gormLogger.Warn
	default:
		gormLogLevel = gormLogger.Error
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogLevel),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Auto migrate the schema
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Initialize default data
	if err := initializeDefaultData(db); err != nil {
		return nil, fmt.Errorf("failed to initialize default data: %w", err)
	}

	logger.Info("Database initialized successfully")
	return db, nil
}

func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.UserPermission{},
		&models.RolePermission{},
		&models.Application{},
		&models.ApplicationEvent{},
		&models.Repository{},
		&models.Cluster{},
		&models.Project{},
	)
}

func initializeDefaultData(db *gorm.DB) error {
	// Initialize default permissions
	permissions := []models.Permission{
		{Name: "applications.read", Resource: "applications", Action: "read", Description: "Read applications"},
		{Name: "applications.write", Resource: "applications", Action: "write", Description: "Create and update applications"},
		{Name: "applications.delete", Resource: "applications", Action: "delete", Description: "Delete applications"},
		{Name: "applications.sync", Resource: "applications", Action: "sync", Description: "Sync applications"},
		{Name: "repositories.read", Resource: "repositories", Action: "read", Description: "Read repositories"},
		{Name: "repositories.write", Resource: "repositories", Action: "write", Description: "Create and update repositories"},
		{Name: "repositories.delete", Resource: "repositories", Action: "delete", Description: "Delete repositories"},
		{Name: "clusters.read", Resource: "clusters", Action: "read", Description: "Read clusters"},
		{Name: "clusters.write", Resource: "clusters", Action: "write", Description: "Create and update clusters"},
		{Name: "clusters.delete", Resource: "clusters", Action: "delete", Description: "Delete clusters"},
		{Name: "projects.read", Resource: "projects", Action: "read", Description: "Read projects"},
		{Name: "projects.write", Resource: "projects", Action: "write", Description: "Create and update projects"},
		{Name: "projects.delete", Resource: "projects", Action: "delete", Description: "Delete projects"},
		{Name: "users.read", Resource: "users", Action: "read", Description: "Read users"},
		{Name: "users.write", Resource: "users", Action: "write", Description: "Create and update users"},
		{Name: "users.delete", Resource: "users", Action: "delete", Description: "Delete users"},
		{Name: "roles.read", Resource: "roles", Action: "read", Description: "Read roles"},
		{Name: "roles.write", Resource: "roles", Action: "write", Description: "Create and update roles"},
		{Name: "roles.delete", Resource: "roles", Action: "delete", Description: "Delete roles"},
	}

	for _, perm := range permissions {
		var existingPerm models.Permission
		if err := db.Where("name = ?", perm.Name).First(&existingPerm).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&perm).Error; err != nil {
					return fmt.Errorf("failed to create permission %s: %w", perm.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check permission %s: %w", perm.Name, err)
			}
		}
	}

	// Initialize default roles
	roles := []models.Role{
		{Name: "admin", Description: "Administrator with full access", IsActive: true},
		{Name: "developer", Description: "Developer with application management access", IsActive: true},
		{Name: "viewer", Description: "Read-only access to applications", IsActive: true},
	}

	for _, role := range roles {
		var existingRole models.Role
		if err := db.Where("name = ?", role.Name).First(&existingRole).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&role).Error; err != nil {
					return fmt.Errorf("failed to create role %s: %w", role.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check role %s: %w", role.Name, err)
			}
		}
	}

	// Assign permissions to roles
	if err := assignRolePermissions(db); err != nil {
		return fmt.Errorf("failed to assign role permissions: %w", err)
	}

	// Create default admin user
	if err := createDefaultAdmin(db); err != nil {
		return fmt.Errorf("failed to create default admin: %w", err)
	}

	return nil
}

func assignRolePermissions(db *gorm.DB) error {
	// Admin role gets all permissions
	var adminRole models.Role
	if err := db.Where("name = ?", "admin").First(&adminRole).Error; err != nil {
		return err
	}

	var allPermissions []models.Permission
	if err := db.Find(&allPermissions).Error; err != nil {
		return err
	}

	if err := db.Model(&adminRole).Association("Permissions").Replace(allPermissions); err != nil {
		return err
	}

	// Developer role gets application, repository, and cluster read/write permissions
	var developerRole models.Role
	if err := db.Where("name = ?", "developer").First(&developerRole).Error; err != nil {
		return err
	}

	var developerPermissions []models.Permission
	developerPerms := []string{
		"applications.read", "applications.write", "applications.sync",
		"repositories.read", "repositories.write",
		"clusters.read", "projects.read",
	}

	if err := db.Where("name IN ?", developerPerms).Find(&developerPermissions).Error; err != nil {
		return err
	}

	if err := db.Model(&developerRole).Association("Permissions").Replace(developerPermissions); err != nil {
		return err
	}

	// Viewer role gets read-only permissions
	var viewerRole models.Role
	if err := db.Where("name = ?", "viewer").First(&viewerRole).Error; err != nil {
		return err
	}

	var viewerPermissions []models.Permission
	viewerPerms := []string{
		"applications.read", "repositories.read", "clusters.read", "projects.read",
	}

	if err := db.Where("name IN ?", viewerPerms).Find(&viewerPermissions).Error; err != nil {
		return err
	}

	if err := db.Model(&viewerRole).Association("Permissions").Replace(viewerPermissions); err != nil {
		return err
	}

	return nil
}

func createDefaultAdmin(db *gorm.DB) error {
	var existingAdmin models.User
	if err := db.Where("username = ?", "admin").First(&existingAdmin).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			admin := models.User{
				Username:  "admin",
				Email:     "<EMAIL>",
				Password:  "admin123", // Will be hashed by BeforeCreate hook
				FirstName: "System",
				LastName:  "Administrator",
				IsActive:  true,
				IsAdmin:   true,
			}

			if err := db.Create(&admin).Error; err != nil {
				return fmt.Errorf("failed to create admin user: %w", err)
			}

			// Assign admin role
			var adminRole models.Role
			if err := db.Where("name = ?", "admin").First(&adminRole).Error; err != nil {
				return err
			}

			if err := db.Model(&admin).Association("Roles").Append(&adminRole); err != nil {
				return err
			}

			logger.Info("Default admin user created: username=admin, password=admin123")
		} else {
			return fmt.Errorf("failed to check admin user: %w", err)
		}
	}

	return nil
}
