package service

import (
	"context"
	"gitops-platform/internal/models"
	"gitops-platform/pkg/logger"
	"github.com/argoproj/argo-cd/v2/pkg/apis/application/v1alpha1"
	"gorm.io/gorm"
)

type ClusterService interface {
	GetCluster(id uint) (*models.Cluster, error)
	GetClusterByServer(server string) (*models.Cluster, error)
	ListClusters(offset, limit int) ([]models.Cluster, int64, error)
	CreateCluster(cluster *models.Cluster) error
	UpdateCluster(cluster *models.Cluster) error
	DeleteCluster(id uint) error
	TestConnection(id uint) error
}

type clusterService struct {
	db           *gorm.DB
	argoCDClient ArgoCDService
}

func NewClusterService(db *gorm.DB, argoCDClient ArgoCDService) ClusterService {
	return &clusterService{
		db:           db,
		argoCDClient: argoCDClient,
	}
}

func (s *clusterService) GetCluster(id uint) (*models.Cluster, error) {
	var cluster models.Cluster
	if err := s.db.Where("id = ?", id).First(&cluster).Error; err != nil {
		return nil, err
	}
	return &cluster, nil
}

func (s *clusterService) GetClusterByServer(server string) (*models.Cluster, error) {
	var cluster models.Cluster
	if err := s.db.Where("server = ?", server).First(&cluster).Error; err != nil {
		return nil, err
	}
	return &cluster, nil
}

func (s *clusterService) ListClusters(offset, limit int) ([]models.Cluster, int64, error) {
	var clusters []models.Cluster
	var total int64

	// Count total clusters
	if err := s.db.Model(&models.Cluster{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get clusters with pagination
	if err := s.db.Offset(offset).Limit(limit).Find(&clusters).Error; err != nil {
		return nil, 0, err
	}

	return clusters, total, nil
}

func (s *clusterService) CreateCluster(cluster *models.Cluster) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create cluster in database
	if err := tx.Create(cluster).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create cluster in Argo CD
	argoCDCluster := s.convertToArgoCDCluster(cluster)
	_, err := s.argoCDClient.CreateCluster(context.Background(), argoCDCluster)
	if err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Cluster created successfully: %s", cluster.Name)
	return nil
}

func (s *clusterService) UpdateCluster(cluster *models.Cluster) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update cluster in database
	if err := tx.Save(cluster).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update cluster in Argo CD
	argoCDCluster := s.convertToArgoCDCluster(cluster)
	_, err := s.argoCDClient.UpdateCluster(context.Background(), argoCDCluster)
	if err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Cluster updated successfully: %s", cluster.Name)
	return nil
}

func (s *clusterService) DeleteCluster(id uint) error {
	var cluster models.Cluster
	if err := s.db.First(&cluster, id).Error; err != nil {
		return err
	}

	// Delete cluster from Argo CD
	if err := s.argoCDClient.DeleteCluster(context.Background(), cluster.Server); err != nil {
		logger.Error("Failed to delete cluster from Argo CD: %v", err)
		// Continue with database deletion even if Argo CD deletion fails
	}

	// Delete cluster from database
	if err := s.db.Delete(&cluster).Error; err != nil {
		return err
	}

	logger.Info("Cluster deleted successfully: %s", cluster.Name)
	return nil
}

func (s *clusterService) TestConnection(id uint) error {
	var cluster models.Cluster
	if err := s.db.First(&cluster, id).Error; err != nil {
		return err
	}

	// Test connection by trying to get cluster from Argo CD
	_, err := s.argoCDClient.GetCluster(context.Background(), cluster.Server)
	if err != nil {
		// Update connection status
		cluster.ConnectionStatus = "failed"
		s.db.Save(&cluster)
		return err
	}

	// Update connection status
	cluster.ConnectionStatus = "connected"
	s.db.Save(&cluster)

	return nil
}

func (s *clusterService) convertToArgoCDCluster(cluster *models.Cluster) *v1alpha1.Cluster {
	argoCDCluster := &v1alpha1.Cluster{
		Name:   cluster.Name,
		Server: cluster.Server,
	}

	// Set cluster configuration
	if cluster.Config != "" {
		argoCDCluster.Config = v1alpha1.ClusterConfig{
			// Note: In a real implementation, you would need to properly
			// parse and set the cluster configuration from the encrypted config
		}
	}

	return argoCDCluster
}
