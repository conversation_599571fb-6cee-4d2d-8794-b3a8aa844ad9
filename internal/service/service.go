package service

import (
	"gitops-platform/internal/config"
	"gorm.io/gorm"
)

// Services holds all service instances
type Services struct {
	Auth        AuthService
	User        UserService
	Application ApplicationService
	Repository  RepositoryService
	Cluster     ClusterService
	Project     ProjectService
	ArgoCD      ArgoCDService
}

// NewServices creates a new Services instance
func NewServices(db *gorm.DB, cfg *config.Config) *Services {
	// Initialize ArgoCD client
	argoCDClient := NewArgoCDClient(cfg.ArgoCD)

	return &Services{
		Auth:        NewAuthService(db, cfg.Auth),
		User:        NewUserService(db),
		Application: NewApplicationService(db, argoCDClient),
		Repository:  NewRepositoryService(db, argoCDClient),
		Cluster:     NewClusterService(db, argoCDClient),
		Project:     NewProjectService(db, argoCDClient),
		ArgoCD:      argoCDClient,
	}
}
