package service

import (
	"context"
	"gitops-platform/internal/models"
	"gitops-platform/pkg/logger"
	"github.com/argoproj/argo-cd/v2/pkg/apis/application/v1alpha1"
	"gorm.io/gorm"
)

type RepositoryService interface {
	GetRepository(id uint) (*models.Repository, error)
	GetRepositoryByURL(url string) (*models.Repository, error)
	ListRepositories(offset, limit int) ([]models.Repository, int64, error)
	CreateRepository(repo *models.Repository) error
	UpdateRepository(repo *models.Repository) error
	DeleteRepository(id uint) error
	TestConnection(id uint) error
}

type repositoryService struct {
	db           *gorm.DB
	argoCDClient ArgoCDService
}

func NewRepositoryService(db *gorm.DB, argoCDClient ArgoCDService) RepositoryService {
	return &repositoryService{
		db:           db,
		argoCDClient: argoCDClient,
	}
}

func (s *repositoryService) GetRepository(id uint) (*models.Repository, error) {
	var repo models.Repository
	if err := s.db.Where("id = ?", id).First(&repo).Error; err != nil {
		return nil, err
	}
	return &repo, nil
}

func (s *repositoryService) GetRepositoryByURL(url string) (*models.Repository, error) {
	var repo models.Repository
	if err := s.db.Where("url = ?", url).First(&repo).Error; err != nil {
		return nil, err
	}
	return &repo, nil
}

func (s *repositoryService) ListRepositories(offset, limit int) ([]models.Repository, int64, error) {
	var repos []models.Repository
	var total int64

	// Count total repositories
	if err := s.db.Model(&models.Repository{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get repositories with pagination
	if err := s.db.Offset(offset).Limit(limit).Find(&repos).Error; err != nil {
		return nil, 0, err
	}

	return repos, total, nil
}

func (s *repositoryService) CreateRepository(repo *models.Repository) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create repository in database
	if err := tx.Create(repo).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create repository in Argo CD
	argoCDRepo := s.convertToArgoCDRepository(repo)
	_, err := s.argoCDClient.CreateRepository(context.Background(), argoCDRepo)
	if err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Repository created successfully: %s", repo.Name)
	return nil
}

func (s *repositoryService) UpdateRepository(repo *models.Repository) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update repository in database
	if err := tx.Save(repo).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update repository in Argo CD
	argoCDRepo := s.convertToArgoCDRepository(repo)
	_, err := s.argoCDClient.UpdateRepository(context.Background(), argoCDRepo)
	if err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Repository updated successfully: %s", repo.Name)
	return nil
}

func (s *repositoryService) DeleteRepository(id uint) error {
	var repo models.Repository
	if err := s.db.First(&repo, id).Error; err != nil {
		return err
	}

	// Delete repository from Argo CD
	if err := s.argoCDClient.DeleteRepository(context.Background(), repo.URL); err != nil {
		logger.Error("Failed to delete repository from Argo CD: %v", err)
		// Continue with database deletion even if Argo CD deletion fails
	}

	// Delete repository from database
	if err := s.db.Delete(&repo).Error; err != nil {
		return err
	}

	logger.Info("Repository deleted successfully: %s", repo.Name)
	return nil
}

func (s *repositoryService) TestConnection(id uint) error {
	var repo models.Repository
	if err := s.db.First(&repo, id).Error; err != nil {
		return err
	}

	// Test connection by trying to get repository from Argo CD
	_, err := s.argoCDClient.GetRepository(context.Background(), repo.URL)
	if err != nil {
		// Update connection status
		repo.ConnectionStatus = "failed"
		s.db.Save(&repo)
		return err
	}

	// Update connection status
	repo.ConnectionStatus = "connected"
	s.db.Save(&repo)

	return nil
}

func (s *repositoryService) convertToArgoCDRepository(repo *models.Repository) *v1alpha1.Repository {
	argoCDRepo := &v1alpha1.Repository{
		Repo: repo.URL,
		Type: repo.Type,
	}

	// Set authentication
	if repo.Username != "" {
		argoCDRepo.Username = repo.Username
	}
	if repo.Password != "" {
		argoCDRepo.Password = repo.Password
	}
	if repo.SSHPrivateKey != "" {
		argoCDRepo.SSHPrivateKey = repo.SSHPrivateKey
	}
	if repo.TLSClientCert != "" {
		argoCDRepo.TLSClientCertData = repo.TLSClientCert
	}
	if repo.TLSClientKey != "" {
		argoCDRepo.TLSClientCertKey = repo.TLSClientKey
	}

	return argoCDRepo
}
