package service

import (
	"errors"
	"gitops-platform/internal/models"
	"gorm.io/gorm"
)

type UserService interface {
	GetUser(id uint) (*models.User, error)
	GetUserByUsername(username string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	ListUsers(offset, limit int) ([]models.User, int64, error)
	CreateUser(user *models.User) error
	UpdateUser(user *models.User) error
	DeleteUser(id uint) error
	ActivateUser(id uint) error
	DeactivateUser(id uint) error
	AssignRole(userID, roleID uint) error
	RemoveRole(userID, roleID uint) error
	AssignPermission(userID, permissionID uint) error
	RemovePermission(userID, permissionID uint) error
}

type userService struct {
	db *gorm.DB
}

func NewUserService(db *gorm.DB) UserService {
	return &userService{db: db}
}

func (s *userService) GetUser(id uint) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Roles").Preload("Permissions").
		Where("id = ?", id).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (s *userService) GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Roles").Preload("Permissions").
		Where("username = ?", username).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (s *userService) GetUserByEmail(email string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("Roles").Preload("Permissions").
		Where("email = ?", email).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (s *userService) ListUsers(offset, limit int) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	// Count total users
	if err := s.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get users with pagination
	if err := s.db.Preload("Roles").Preload("Permissions").
		Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

func (s *userService) CreateUser(user *models.User) error {
	// Check if username already exists
	var existingUser models.User
	if err := s.db.Where("username = ?", user.Username).First(&existingUser).Error; err == nil {
		return errors.New("username already exists")
	}

	// Check if email already exists
	if err := s.db.Where("email = ?", user.Email).First(&existingUser).Error; err == nil {
		return errors.New("email already exists")
	}

	return s.db.Create(user).Error
}

func (s *userService) UpdateUser(user *models.User) error {
	// Check if username already exists for other users
	var existingUser models.User
	if err := s.db.Where("username = ? AND id != ?", user.Username, user.ID).First(&existingUser).Error; err == nil {
		return errors.New("username already exists")
	}

	// Check if email already exists for other users
	if err := s.db.Where("email = ? AND id != ?", user.Email, user.ID).First(&existingUser).Error; err == nil {
		return errors.New("email already exists")
	}

	return s.db.Save(user).Error
}

func (s *userService) DeleteUser(id uint) error {
	return s.db.Delete(&models.User{}, id).Error
}

func (s *userService) ActivateUser(id uint) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Update("is_active", true).Error
}

func (s *userService) DeactivateUser(id uint) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Update("is_active", false).Error
}

func (s *userService) AssignRole(userID, roleID uint) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	var role models.Role
	if err := s.db.First(&role, roleID).Error; err != nil {
		return err
	}

	return s.db.Model(&user).Association("Roles").Append(&role)
}

func (s *userService) RemoveRole(userID, roleID uint) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	var role models.Role
	if err := s.db.First(&role, roleID).Error; err != nil {
		return err
	}

	return s.db.Model(&user).Association("Roles").Delete(&role)
}

func (s *userService) AssignPermission(userID, permissionID uint) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	var permission models.Permission
	if err := s.db.First(&permission, permissionID).Error; err != nil {
		return err
	}

	return s.db.Model(&user).Association("Permissions").Append(&permission)
}

func (s *userService) RemovePermission(userID, permissionID uint) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	var permission models.Permission
	if err := s.db.First(&permission, permissionID).Error; err != nil {
		return err
	}

	return s.db.Model(&user).Association("Permissions").Delete(&permission)
}
