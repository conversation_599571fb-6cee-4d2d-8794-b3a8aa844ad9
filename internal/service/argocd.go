package service

import (
	"context"
	"crypto/tls"
	"fmt"
	"gitops-platform/internal/config"
	"gitops-platform/pkg/logger"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"

	"github.com/argoproj/argo-cd/v2/pkg/apiclient"
	"github.com/argoproj/argo-cd/v2/pkg/apiclient/application"
	"github.com/argoproj/argo-cd/v2/pkg/apiclient/cluster"
	"github.com/argoproj/argo-cd/v2/pkg/apiclient/project"
	"github.com/argoproj/argo-cd/v2/pkg/apiclient/repository"
	"github.com/argoproj/argo-cd/v2/pkg/apis/application/v1alpha1"
)

type ArgoCDService interface {
	// Application operations
	GetApplication(ctx context.Context, name string) (*v1alpha1.Application, error)
	ListApplications(ctx context.Context) (*v1alpha1.ApplicationList, error)
	CreateApplication(ctx context.Context, app *v1alpha1.Application) (*v1alpha1.Application, error)
	UpdateApplication(ctx context.Context, app *v1alpha1.Application) (*v1alpha1.Application, error)
	DeleteApplication(ctx context.Context, name string) error
	SyncApplication(ctx context.Context, name string) (*v1alpha1.Application, error)

	// Repository operations
	GetRepository(ctx context.Context, url string) (*v1alpha1.Repository, error)
	ListRepositories(ctx context.Context) (*v1alpha1.RepositoryList, error)
	CreateRepository(ctx context.Context, repo *v1alpha1.Repository) (*v1alpha1.Repository, error)
	UpdateRepository(ctx context.Context, repo *v1alpha1.Repository) (*v1alpha1.Repository, error)
	DeleteRepository(ctx context.Context, url string) error

	// Cluster operations
	GetCluster(ctx context.Context, server string) (*v1alpha1.Cluster, error)
	ListClusters(ctx context.Context) (*v1alpha1.ClusterList, error)
	CreateCluster(ctx context.Context, cluster *v1alpha1.Cluster) (*v1alpha1.Cluster, error)
	UpdateCluster(ctx context.Context, cluster *v1alpha1.Cluster) (*v1alpha1.Cluster, error)
	DeleteCluster(ctx context.Context, server string) error

	// Project operations
	GetProject(ctx context.Context, name string) (*v1alpha1.AppProject, error)
	ListProjects(ctx context.Context) (*v1alpha1.AppProjectList, error)
	CreateProject(ctx context.Context, project *v1alpha1.AppProject) (*v1alpha1.AppProject, error)
	UpdateProject(ctx context.Context, project *v1alpha1.AppProject) (*v1alpha1.AppProject, error)
	DeleteProject(ctx context.Context, name string) error

	// Health check
	HealthCheck(ctx context.Context) error
}

type argoCDService struct {
	client apiclient.Client
	cfg    config.ArgoCDConfig
}

func NewArgoCDClient(cfg config.ArgoCDConfig) ArgoCDService {
	return &argoCDService{
		cfg: cfg,
	}
}

func (s *argoCDService) getClient() (apiclient.Client, error) {
	if s.client != nil {
		return s.client, nil
	}

	opts := apiclient.ClientOptions{
		ServerAddr: s.cfg.Server,
		Insecure:   s.cfg.Insecure,
	}

	if s.cfg.Token != "" {
		opts.AuthToken = s.cfg.Token
	} else if s.cfg.Username != "" && s.cfg.Password != "" {
		opts.Username = s.cfg.Username
		opts.Password = s.cfg.Password
	}

	// Configure TLS
	if !s.cfg.Insecure {
		opts.GRPCDialOptions = []grpc.DialOption{
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		}
	} else {
		opts.GRPCDialOptions = []grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		}
	}

	client, err := apiclient.NewClient(&opts)
	if err != nil {
		return nil, fmt.Errorf("failed to create ArgoCD client: %w", err)
	}

	s.client = client
	return client, nil
}

func (s *argoCDService) getContext() context.Context {
	ctx := context.Background()
	if s.cfg.Token != "" {
		ctx = metadata.AppendToOutgoingContext(ctx, "authorization", "Bearer "+s.cfg.Token)
	}
	return ctx
}

// Application operations
func (s *argoCDService) GetApplication(ctx context.Context, name string) (*v1alpha1.Application, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, appClient, err := client.NewApplicationClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return appClient.Get(s.getContext(), &application.ApplicationQuery{Name: &name})
}

func (s *argoCDService) ListApplications(ctx context.Context) (*v1alpha1.ApplicationList, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, appClient, err := client.NewApplicationClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return appClient.List(s.getContext(), &application.ApplicationQuery{})
}

func (s *argoCDService) CreateApplication(ctx context.Context, app *v1alpha1.Application) (*v1alpha1.Application, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, appClient, err := client.NewApplicationClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return appClient.Create(s.getContext(), &application.ApplicationCreateRequest{Application: app})
}

func (s *argoCDService) UpdateApplication(ctx context.Context, app *v1alpha1.Application) (*v1alpha1.Application, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, appClient, err := client.NewApplicationClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return appClient.Update(s.getContext(), &application.ApplicationUpdateRequest{Application: app})
}

func (s *argoCDService) DeleteApplication(ctx context.Context, name string) error {
	client, err := s.getClient()
	if err != nil {
		return err
	}

	conn, appClient, err := client.NewApplicationClient()
	if err != nil {
		return err
	}
	defer conn.Close()

	cascade := true
	_, err = appClient.Delete(s.getContext(), &application.ApplicationDeleteRequest{
		Name:    &name,
		Cascade: &cascade,
	})
	return err
}

func (s *argoCDService) SyncApplication(ctx context.Context, name string) (*v1alpha1.Application, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, appClient, err := client.NewApplicationClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return appClient.Sync(s.getContext(), &application.ApplicationSyncRequest{Name: &name})
}

// Repository operations
func (s *argoCDService) GetRepository(ctx context.Context, url string) (*v1alpha1.Repository, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, repoClient, err := client.NewRepoClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return repoClient.Get(s.getContext(), &repository.RepoQuery{Repo: url})
}

func (s *argoCDService) ListRepositories(ctx context.Context) (*v1alpha1.RepositoryList, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, repoClient, err := client.NewRepoClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return repoClient.ListRepositories(s.getContext(), &repository.RepoQuery{})
}

func (s *argoCDService) CreateRepository(ctx context.Context, repo *v1alpha1.Repository) (*v1alpha1.Repository, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, repoClient, err := client.NewRepoClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return repoClient.CreateRepository(s.getContext(), &repository.RepoCreateRequest{Repo: repo})
}

func (s *argoCDService) UpdateRepository(ctx context.Context, repo *v1alpha1.Repository) (*v1alpha1.Repository, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, repoClient, err := client.NewRepoClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return repoClient.UpdateRepository(s.getContext(), &repository.RepoUpdateRequest{Repo: repo})
}

func (s *argoCDService) DeleteRepository(ctx context.Context, url string) error {
	client, err := s.getClient()
	if err != nil {
		return err
	}

	conn, repoClient, err := client.NewRepoClient()
	if err != nil {
		return err
	}
	defer conn.Close()

	_, err = repoClient.DeleteRepository(s.getContext(), &repository.RepoQuery{Repo: url})
	return err
}

// Cluster operations
func (s *argoCDService) GetCluster(ctx context.Context, server string) (*v1alpha1.Cluster, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, clusterClient, err := client.NewClusterClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return clusterClient.Get(s.getContext(), &cluster.ClusterQuery{Server: server})
}

func (s *argoCDService) ListClusters(ctx context.Context) (*v1alpha1.ClusterList, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, clusterClient, err := client.NewClusterClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return clusterClient.List(s.getContext(), &cluster.ClusterQuery{})
}

func (s *argoCDService) CreateCluster(ctx context.Context, clusterObj *v1alpha1.Cluster) (*v1alpha1.Cluster, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, clusterClient, err := client.NewClusterClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return clusterClient.Create(s.getContext(), &cluster.ClusterCreateRequest{Cluster: clusterObj})
}

func (s *argoCDService) UpdateCluster(ctx context.Context, clusterObj *v1alpha1.Cluster) (*v1alpha1.Cluster, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, clusterClient, err := client.NewClusterClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return clusterClient.Update(s.getContext(), &cluster.ClusterUpdateRequest{Cluster: clusterObj})
}

func (s *argoCDService) DeleteCluster(ctx context.Context, server string) error {
	client, err := s.getClient()
	if err != nil {
		return err
	}

	conn, clusterClient, err := client.NewClusterClient()
	if err != nil {
		return err
	}
	defer conn.Close()

	_, err = clusterClient.Delete(s.getContext(), &cluster.ClusterQuery{Server: server})
	return err
}

// Project operations
func (s *argoCDService) GetProject(ctx context.Context, name string) (*v1alpha1.AppProject, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, projectClient, err := client.NewProjectClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return projectClient.Get(s.getContext(), &project.ProjectQuery{Name: name})
}

func (s *argoCDService) ListProjects(ctx context.Context) (*v1alpha1.AppProjectList, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, projectClient, err := client.NewProjectClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return projectClient.List(s.getContext(), &project.ProjectQuery{})
}

func (s *argoCDService) CreateProject(ctx context.Context, projectObj *v1alpha1.AppProject) (*v1alpha1.AppProject, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, projectClient, err := client.NewProjectClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return projectClient.Create(s.getContext(), &project.ProjectCreateRequest{Project: projectObj})
}

func (s *argoCDService) UpdateProject(ctx context.Context, projectObj *v1alpha1.AppProject) (*v1alpha1.AppProject, error) {
	client, err := s.getClient()
	if err != nil {
		return nil, err
	}

	conn, projectClient, err := client.NewProjectClient()
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	return projectClient.Update(s.getContext(), &project.ProjectUpdateRequest{Project: projectObj})
}

func (s *argoCDService) DeleteProject(ctx context.Context, name string) error {
	client, err := s.getClient()
	if err != nil {
		return err
	}

	conn, projectClient, err := client.NewProjectClient()
	if err != nil {
		return err
	}
	defer conn.Close()

	_, err = projectClient.Delete(s.getContext(), &project.ProjectQuery{Name: name})
	return err
}

// Health check
func (s *argoCDService) HealthCheck(ctx context.Context) error {
	client, err := s.getClient()
	if err != nil {
		return err
	}

	// Try to list applications as a health check
	conn, appClient, err := client.NewApplicationClient()
	if err != nil {
		return err
	}
	defer conn.Close()

	_, err = appClient.List(s.getContext(), &application.ApplicationQuery{})
	if err != nil {
		logger.Error("ArgoCD health check failed: %v", err)
		return err
	}

	return nil
}
