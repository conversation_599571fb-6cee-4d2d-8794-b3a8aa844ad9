package service

import (
	"errors"
	"time"
	"gitops-platform/internal/config"
	"gitops-platform/internal/models"
	"gitops-platform/pkg/logger"
	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"
)

type AuthService interface {
	Login(username, password string) (*LoginResponse, error)
	RefreshToken(refreshToken string) (*TokenResponse, error)
	ValidateToken(tokenString string) (*jwt.Token, error)
	GetUserFromToken(token *jwt.Token) (*models.User, error)
}

type authService struct {
	db  *gorm.DB
	cfg config.AuthConfig
}

type LoginResponse struct {
	User         *models.User   `json:"user"`
	AccessToken  string         `json:"access_token"`
	RefreshToken string         `json:"refresh_token"`
	ExpiresIn    int            `json:"expires_in"`
}

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
}

type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	IsAdmin  bool   `json:"is_admin"`
	jwt.RegisteredClaims
}

func NewAuthService(db *gorm.DB, cfg config.AuthConfig) AuthService {
	return &authService{
		db:  db,
		cfg: cfg,
	}
}

func (s *authService) Login(username, password string) (*LoginResponse, error) {
	var user models.User
	if err := s.db.Preload("Roles").Preload("Permissions").
		Where("username = ? AND is_active = ?", username, true).
		First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("invalid credentials")
		}
		logger.Error("Failed to find user: %v", err)
		return nil, errors.New("authentication failed")
	}

	if !user.CheckPassword(password) {
		return nil, errors.New("invalid credentials")
	}

	// Update last login time
	now := time.Now()
	user.LastLogin = &now
	s.db.Save(&user)

	// Generate tokens
	accessToken, err := s.generateAccessToken(&user)
	if err != nil {
		logger.Error("Failed to generate access token: %v", err)
		return nil, errors.New("token generation failed")
	}

	refreshToken, err := s.generateRefreshToken(&user)
	if err != nil {
		logger.Error("Failed to generate refresh token: %v", err)
		return nil, errors.New("token generation failed")
	}

	return &LoginResponse{
		User:         &user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    s.cfg.TokenExpiry,
	}, nil
}

func (s *authService) RefreshToken(refreshToken string) (*TokenResponse, error) {
	token, err := jwt.ParseWithClaims(refreshToken, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.cfg.JWTSecret), nil
	})

	if err != nil || !token.Valid {
		return nil, errors.New("invalid refresh token")
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	// Get user from database
	var user models.User
	if err := s.db.Where("id = ? AND is_active = ?", claims.UserID, true).
		First(&user).Error; err != nil {
		return nil, errors.New("user not found")
	}

	// Generate new tokens
	accessToken, err := s.generateAccessToken(&user)
	if err != nil {
		return nil, errors.New("token generation failed")
	}

	newRefreshToken, err := s.generateRefreshToken(&user)
	if err != nil {
		return nil, errors.New("token generation failed")
	}

	return &TokenResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresIn:    s.cfg.TokenExpiry,
	}, nil
}

func (s *authService) ValidateToken(tokenString string) (*jwt.Token, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.cfg.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	return token, nil
}

func (s *authService) GetUserFromToken(token *jwt.Token) (*models.User, error) {
	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	var user models.User
	if err := s.db.Preload("Roles").Preload("Permissions").
		Where("id = ? AND is_active = ?", claims.UserID, true).
		First(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

func (s *authService) generateAccessToken(user *models.User) (string, error) {
	claims := Claims{
		UserID:   user.ID,
		Username: user.Username,
		IsAdmin:  user.IsAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(s.cfg.TokenExpiry) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "gitops-platform",
			Subject:   user.Username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWTSecret))
}

func (s *authService) generateRefreshToken(user *models.User) (string, error) {
	claims := Claims{
		UserID:   user.ID,
		Username: user.Username,
		IsAdmin:  user.IsAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(s.cfg.RefreshExpiry) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "gitops-platform",
			Subject:   user.Username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.cfg.JWTSecret))
}
