package service

import (
	"context"
	"encoding/json"
	"fmt"
	"gitops-platform/internal/models"
	"gitops-platform/pkg/logger"
	"time"

	"github.com/argoproj/argo-cd/v2/pkg/apis/application/v1alpha1"
	"gorm.io/gorm"
)

type ApplicationService interface {
	GetApplication(id uint) (*models.Application, error)
	GetApplicationByName(name string) (*models.Application, error)
	ListApplications(offset, limit int) ([]models.Application, int64, error)
	CreateApplication(app *models.Application) error
	UpdateApplication(app *models.Application) error
	DeleteApplication(id uint) error
	SyncApplication(id uint) error
	SyncApplicationStatus(id uint) error
	GetApplicationEvents(id uint, offset, limit int) ([]models.ApplicationEvent, int64, error)
}

type applicationService struct {
	db           *gorm.DB
	argoCDClient ArgoCDService
}

func NewApplicationService(db *gorm.DB, argoCDClient ArgoCDService) ApplicationService {
	return &applicationService{
		db:           db,
		argoCDClient: argoCDClient,
	}
}

func (s *applicationService) GetApplication(id uint) (*models.Application, error) {
	var app models.Application
	if err := s.db.Preload("Cluster").Preload("Events").
		Where("id = ?", id).First(&app).Error; err != nil {
		return nil, err
	}
	return &app, nil
}

func (s *applicationService) GetApplicationByName(name string) (*models.Application, error) {
	var app models.Application
	if err := s.db.Preload("Cluster").Preload("Events").
		Where("name = ?", name).First(&app).Error; err != nil {
		return nil, err
	}
	return &app, nil
}

func (s *applicationService) ListApplications(offset, limit int) ([]models.Application, int64, error) {
	var apps []models.Application
	var total int64

	// Count total applications
	if err := s.db.Model(&models.Application{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get applications with pagination
	if err := s.db.Preload("Cluster").
		Offset(offset).Limit(limit).Find(&apps).Error; err != nil {
		return nil, 0, err
	}

	return apps, total, nil
}

func (s *applicationService) CreateApplication(app *models.Application) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create application in database
	if err := tx.Create(app).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create application in Argo CD
	argoCDApp, err := s.convertToArgoCDApplication(app)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to convert application: %w", err)
	}

	createdApp, err := s.argoCDClient.CreateApplication(context.Background(), argoCDApp)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create application in Argo CD: %w", err)
	}

	// Update application with Argo CD metadata
	s.updateApplicationFromArgoCD(app, createdApp)
	if err := tx.Save(app).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Application created successfully: %s", app.Name)
	return nil
}

func (s *applicationService) UpdateApplication(app *models.Application) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update application in database
	if err := tx.Save(app).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update application in Argo CD
	argoCDApp, err := s.convertToArgoCDApplication(app)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to convert application: %w", err)
	}

	updatedApp, err := s.argoCDClient.UpdateApplication(context.Background(), argoCDApp)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update application in Argo CD: %w", err)
	}

	// Update application with Argo CD metadata
	s.updateApplicationFromArgoCD(app, updatedApp)
	if err := tx.Save(app).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Application updated successfully: %s", app.Name)
	return nil
}

func (s *applicationService) DeleteApplication(id uint) error {
	var app models.Application
	if err := s.db.First(&app, id).Error; err != nil {
		return err
	}

	// Delete application from Argo CD
	if err := s.argoCDClient.DeleteApplication(context.Background(), app.Name); err != nil {
		logger.Error("Failed to delete application from Argo CD: %v", err)
		// Continue with database deletion even if Argo CD deletion fails
	}

	// Delete application from database
	if err := s.db.Delete(&app).Error; err != nil {
		return err
	}

	logger.Info("Application deleted successfully: %s", app.Name)
	return nil
}

func (s *applicationService) SyncApplication(id uint) error {
	var app models.Application
	if err := s.db.First(&app, id).Error; err != nil {
		return err
	}

	// Sync application in Argo CD
	syncedApp, err := s.argoCDClient.SyncApplication(context.Background(), app.Name)
	if err != nil {
		return fmt.Errorf("failed to sync application in Argo CD: %w", err)
	}

	// Update application status
	s.updateApplicationFromArgoCD(&app, syncedApp)
	if err := s.db.Save(&app).Error; err != nil {
		return err
	}

	// Create sync event
	event := models.ApplicationEvent{
		ApplicationID: app.ID,
		EventType:     "sync",
		Message:       "Application sync initiated",
		Reason:        "Manual sync",
		Component:     "gitops-platform",
		Timestamp:     time.Now(),
	}
	s.db.Create(&event)

	logger.Info("Application synced successfully: %s", app.Name)
	return nil
}

func (s *applicationService) SyncApplicationStatus(id uint) error {
	var app models.Application
	if err := s.db.First(&app, id).Error; err != nil {
		return err
	}

	// Get application status from Argo CD
	argoCDApp, err := s.argoCDClient.GetApplication(context.Background(), app.Name)
	if err != nil {
		return fmt.Errorf("failed to get application from Argo CD: %w", err)
	}

	// Update application status
	s.updateApplicationFromArgoCD(&app, argoCDApp)
	if err := s.db.Save(&app).Error; err != nil {
		return err
	}

	return nil
}

func (s *applicationService) GetApplicationEvents(id uint, offset, limit int) ([]models.ApplicationEvent, int64, error) {
	var events []models.ApplicationEvent
	var total int64

	// Count total events
	if err := s.db.Model(&models.ApplicationEvent{}).
		Where("application_id = ?", id).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get events with pagination
	if err := s.db.Where("application_id = ?", id).
		Order("timestamp DESC").
		Offset(offset).Limit(limit).Find(&events).Error; err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

func (s *applicationService) convertToArgoCDApplication(app *models.Application) (*v1alpha1.Application, error) {
	argoCDApp := &v1alpha1.Application{
		ObjectMeta: v1alpha1.ObjectMeta{
			Name:      app.Name,
			Namespace: app.Namespace,
		},
		Spec: v1alpha1.ApplicationSpec{
			Project: app.Project,
			Source: &v1alpha1.ApplicationSource{
				RepoURL:        app.RepoURL,
				Path:           app.Path,
				TargetRevision: app.TargetRevision,
			},
			Destination: v1alpha1.ApplicationDestination{
				Server:    app.DestinationServer,
				Namespace: app.DestinationNamespace,
			},
		},
	}

	// Set sync policy
	if app.AutoSync {
		argoCDApp.Spec.SyncPolicy = &v1alpha1.SyncPolicy{
			Automated: &v1alpha1.SyncPolicyAutomated{},
		}
	}

	// Set labels and annotations
	if app.Labels != "" {
		var labels map[string]string
		if err := json.Unmarshal([]byte(app.Labels), &labels); err == nil {
			argoCDApp.ObjectMeta.Labels = labels
		}
	}

	if app.Annotations != "" {
		var annotations map[string]string
		if err := json.Unmarshal([]byte(app.Annotations), &annotations); err == nil {
			argoCDApp.ObjectMeta.Annotations = annotations
		}
	}

	// Set template-specific configurations
	switch app.TemplateType {
	case "helm":
		if app.Values != "" {
			argoCDApp.Spec.Source.Helm = &v1alpha1.ApplicationSourceHelm{
				Values: app.Values,
			}
		}
	case "kustomize":
		argoCDApp.Spec.Source.Kustomize = &v1alpha1.ApplicationSourceKustomize{}
	}

	return argoCDApp, nil
}

func (s *applicationService) updateApplicationFromArgoCD(app *models.Application, argoCDApp *v1alpha1.Application) {
	if argoCDApp.Status.Health.Status != "" {
		app.Health = string(argoCDApp.Status.Health.Status)
	}

	if argoCDApp.Status.Sync.Status != "" {
		app.SyncStatus = string(argoCDApp.Status.Sync.Status)
	}

	if argoCDApp.Status.OperationState != nil && argoCDApp.Status.OperationState.FinishedAt != nil {
		app.LastSyncTime = &argoCDApp.Status.OperationState.FinishedAt.Time
	}
}
