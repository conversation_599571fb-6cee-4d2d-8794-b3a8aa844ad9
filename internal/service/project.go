package service

import (
	"context"
	"encoding/json"
	"gitops-platform/internal/models"
	"gitops-platform/pkg/logger"
	"github.com/argoproj/argo-cd/v2/pkg/apis/application/v1alpha1"
	"gorm.io/gorm"
)

type ProjectService interface {
	GetProject(id uint) (*models.Project, error)
	GetProjectByName(name string) (*models.Project, error)
	ListProjects(offset, limit int) ([]models.Project, int64, error)
	CreateProject(project *models.Project) error
	UpdateProject(project *models.Project) error
	DeleteProject(id uint) error
}

type projectService struct {
	db           *gorm.DB
	argoCDClient ArgoCDService
}

func NewProjectService(db *gorm.DB, argoCDClient ArgoCDService) ProjectService {
	return &projectService{
		db:           db,
		argoCDClient: argoCDClient,
	}
}

func (s *projectService) GetProject(id uint) (*models.Project, error) {
	var project models.Project
	if err := s.db.Where("id = ?", id).First(&project).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

func (s *projectService) GetProjectByName(name string) (*models.Project, error) {
	var project models.Project
	if err := s.db.Where("name = ?", name).First(&project).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

func (s *projectService) ListProjects(offset, limit int) ([]models.Project, int64, error) {
	var projects []models.Project
	var total int64

	// Count total projects
	if err := s.db.Model(&models.Project{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get projects with pagination
	if err := s.db.Offset(offset).Limit(limit).Find(&projects).Error; err != nil {
		return nil, 0, err
	}

	return projects, total, nil
}

func (s *projectService) CreateProject(project *models.Project) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create project in database
	if err := tx.Create(project).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create project in Argo CD
	argoCDProject, err := s.convertToArgoCDProject(project)
	if err != nil {
		tx.Rollback()
		return err
	}

	_, err = s.argoCDClient.CreateProject(context.Background(), argoCDProject)
	if err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Project created successfully: %s", project.Name)
	return nil
}

func (s *projectService) UpdateProject(project *models.Project) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update project in database
	if err := tx.Save(project).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update project in Argo CD
	argoCDProject, err := s.convertToArgoCDProject(project)
	if err != nil {
		tx.Rollback()
		return err
	}

	_, err = s.argoCDClient.UpdateProject(context.Background(), argoCDProject)
	if err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logger.Info("Project updated successfully: %s", project.Name)
	return nil
}

func (s *projectService) DeleteProject(id uint) error {
	var project models.Project
	if err := s.db.First(&project, id).Error; err != nil {
		return err
	}

	// Delete project from Argo CD
	if err := s.argoCDClient.DeleteProject(context.Background(), project.Name); err != nil {
		logger.Error("Failed to delete project from Argo CD: %v", err)
		// Continue with database deletion even if Argo CD deletion fails
	}

	// Delete project from database
	if err := s.db.Delete(&project).Error; err != nil {
		return err
	}

	logger.Info("Project deleted successfully: %s", project.Name)
	return nil
}

func (s *projectService) convertToArgoCDProject(project *models.Project) (*v1alpha1.AppProject, error) {
	argoCDProject := &v1alpha1.AppProject{
		ObjectMeta: v1alpha1.ObjectMeta{
			Name: project.Name,
		},
		Spec: v1alpha1.AppProjectSpec{
			Description: project.Description,
		},
	}

	// Parse source repositories
	if project.SourceRepos != "" {
		var sourceRepos []string
		if err := json.Unmarshal([]byte(project.SourceRepos), &sourceRepos); err == nil {
			argoCDProject.Spec.SourceRepos = sourceRepos
		}
	}

	// Parse destinations
	if project.Destinations != "" {
		var destinations []v1alpha1.ApplicationDestination
		if err := json.Unmarshal([]byte(project.Destinations), &destinations); err == nil {
			argoCDProject.Spec.Destinations = destinations
		}
	}

	// Parse cluster resources
	if project.ClusterResources != "" {
		var clusterResources []v1alpha1.GroupKind
		if err := json.Unmarshal([]byte(project.ClusterResources), &clusterResources); err == nil {
			argoCDProject.Spec.ClusterResourceWhitelist = clusterResources
		}
	}

	// Parse namespaces
	if project.Namespaces != "" {
		var namespaces []string
		if err := json.Unmarshal([]byte(project.Namespaces), &namespaces); err == nil {
			argoCDProject.Spec.NamespaceResourceWhitelist = []v1alpha1.GroupKind{
				{Group: "", Kind: "*"},
			}
		}
	}

	// Parse roles
	if project.Roles != "" {
		var roles []v1alpha1.ProjectRole
		if err := json.Unmarshal([]byte(project.Roles), &roles); err == nil {
			argoCDProject.Spec.Roles = roles
		}
	}

	return argoCDProject, nil
}
