package handler

import (
	"net/http"
	"strconv"
	"gitops-platform/internal/models"
	"gitops-platform/internal/service"
	"gitops-platform/pkg/response"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type ApplicationHandler struct {
	appService service.ApplicationService
	validator  *validator.Validate
}

type CreateApplicationRequest struct {
	Name                 string `json:"name" validate:"required,max=100"`
	Namespace            string `json:"namespace" validate:"required,max=100"`
	Project              string `json:"project" validate:"required,max=100"`
	Description          string `json:"description" validate:"max=500"`
	RepoURL              string `json:"repo_url" validate:"required,url"`
	Path                 string `json:"path" validate:"required"`
	TargetRevision       string `json:"target_revision"`
	DestinationServer    string `json:"destination_server" validate:"required"`
	DestinationNamespace string `json:"destination_namespace" validate:"required"`
	AutoSync             bool   `json:"auto_sync"`
	TemplateType         string `json:"template_type"`
	Values               string `json:"values"`
	ClusterID            uint   `json:"cluster_id" validate:"required"`
}

type UpdateApplicationRequest struct {
	Description          string `json:"description" validate:"max=500"`
	Path                 string `json:"path" validate:"required"`
	TargetRevision       string `json:"target_revision"`
	DestinationNamespace string `json:"destination_namespace" validate:"required"`
	AutoSync             bool   `json:"auto_sync"`
	Values               string `json:"values"`
}

func NewApplicationHandler(appService service.ApplicationService) *ApplicationHandler {
	return &ApplicationHandler{
		appService: appService,
		validator:  validator.New(),
	}
}

// ListApplications godoc
// @Summary List applications
// @Description Get list of applications with pagination
// @Tags applications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} response.PaginatedResponse{data=[]models.Application}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /applications [get]
func (h *ApplicationHandler) ListApplications(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	offset := (page - 1) * limit

	apps, total, err := h.appService.ListApplications(offset, limit)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to list applications", err)
		return
	}

	response.Paginated(c, "Applications retrieved successfully", apps, total, page, limit)
}

// GetApplication godoc
// @Summary Get application
// @Description Get application by ID
// @Tags applications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Application ID"
// @Success 200 {object} response.Response{data=models.Application}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /applications/{id} [get]
func (h *ApplicationHandler) GetApplication(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid application ID", err)
		return
	}

	app, err := h.appService.GetApplication(uint(id))
	if err != nil {
		response.Error(c, http.StatusNotFound, "Application not found", err)
		return
	}

	response.Success(c, "Application retrieved successfully", app)
}

// CreateApplication godoc
// @Summary Create application
// @Description Create a new application
// @Tags applications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateApplicationRequest true "Application data"
// @Success 201 {object} response.Response{data=models.Application}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /applications [post]
func (h *ApplicationHandler) CreateApplication(c *gin.Context) {
	var req CreateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	if err := h.validator.Struct(&req); err != nil {
		response.ValidationError(c, err)
		return
	}

	app := &models.Application{
		Name:                 req.Name,
		Namespace:            req.Namespace,
		Project:              req.Project,
		Description:          req.Description,
		RepoURL:              req.RepoURL,
		Path:                 req.Path,
		TargetRevision:       req.TargetRevision,
		DestinationServer:    req.DestinationServer,
		DestinationNamespace: req.DestinationNamespace,
		AutoSync:             req.AutoSync,
		TemplateType:         req.TemplateType,
		Values:               req.Values,
		ClusterID:            req.ClusterID,
	}

	if app.TargetRevision == "" {
		app.TargetRevision = "HEAD"
	}

	if err := h.appService.CreateApplication(app); err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to create application", err)
		return
	}

	response.Created(c, "Application created successfully", app)
}

// UpdateApplication godoc
// @Summary Update application
// @Description Update an existing application
// @Tags applications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Application ID"
// @Param request body UpdateApplicationRequest true "Application data"
// @Success 200 {object} response.Response{data=models.Application}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /applications/{id} [put]
func (h *ApplicationHandler) UpdateApplication(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid application ID", err)
		return
	}

	var req UpdateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	if err := h.validator.Struct(&req); err != nil {
		response.ValidationError(c, err)
		return
	}

	app, err := h.appService.GetApplication(uint(id))
	if err != nil {
		response.Error(c, http.StatusNotFound, "Application not found", err)
		return
	}

	// Update fields
	app.Description = req.Description
	app.Path = req.Path
	app.TargetRevision = req.TargetRevision
	app.DestinationNamespace = req.DestinationNamespace
	app.AutoSync = req.AutoSync
	app.Values = req.Values

	if app.TargetRevision == "" {
		app.TargetRevision = "HEAD"
	}

	if err := h.appService.UpdateApplication(app); err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to update application", err)
		return
	}

	response.Success(c, "Application updated successfully", app)
}

// DeleteApplication godoc
// @Summary Delete application
// @Description Delete an application
// @Tags applications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Application ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /applications/{id} [delete]
func (h *ApplicationHandler) DeleteApplication(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid application ID", err)
		return
	}

	if err := h.appService.DeleteApplication(uint(id)); err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to delete application", err)
		return
	}

	response.Success(c, "Application deleted successfully", nil)
}

// SyncApplication godoc
// @Summary Sync application
// @Description Trigger application synchronization
// @Tags applications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Application ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /applications/{id}/sync [post]
func (h *ApplicationHandler) SyncApplication(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid application ID", err)
		return
	}

	if err := h.appService.SyncApplication(uint(id)); err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to sync application", err)
		return
	}

	response.Success(c, "Application sync initiated successfully", nil)
}

// GetApplicationEvents godoc
// @Summary Get application events
// @Description Get application events with pagination
// @Tags applications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Application ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} response.PaginatedResponse{data=[]models.ApplicationEvent}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /applications/{id}/events [get]
func (h *ApplicationHandler) GetApplicationEvents(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid application ID", err)
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	offset := (page - 1) * limit

	events, total, err := h.appService.GetApplicationEvents(uint(id), offset, limit)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to get application events", err)
		return
	}

	response.Paginated(c, "Application events retrieved successfully", events, total, page, limit)
}
