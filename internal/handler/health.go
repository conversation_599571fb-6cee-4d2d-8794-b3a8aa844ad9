package handler

import (
	"net/http"
	"time"
	"github.com/gin-gonic/gin"
)

type HealthHandler struct{}

type HealthResponse struct {
	Status    string    `json:"status"`
	Service   string    `json:"service"`
	Version   string    `json:"version"`
	Timestamp time.Time `json:"timestamp"`
	Uptime    string    `json:"uptime"`
}

var startTime = time.Now()

func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// Health godoc
// @Summary Health check
// @Description Get service health status
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /health [get]
func (h *HealthHandler) Health(c *gin.Context) {
	uptime := time.Since(startTime)
	
	response := HealthResponse{
		Status:    "ok",
		Service:   "gitops-platform",
		Version:   "1.0.0",
		Timestamp: time.Now(),
		Uptime:    uptime.String(),
	}
	
	c.JSON(http.StatusOK, response)
}

// Ready godoc
// @Summary Readiness check
// @Description Check if service is ready to serve requests
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /ready [get]
func (h *HealthHandler) Ready(c *gin.Context) {
	// In a real implementation, you would check:
	// - Database connectivity
	// - External service dependencies
	// - Cache availability
	
	response := HealthResponse{
		Status:    "ready",
		Service:   "gitops-platform",
		Version:   "1.0.0",
		Timestamp: time.Now(),
		Uptime:    time.Since(startTime).String(),
	}
	
	c.JSON(http.StatusOK, response)
}
