package models

import (
	"time"
	"gorm.io/gorm"
)

// Application represents an Argo CD application
type Application struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"uniqueIndex;not null" validate:"required,max=100"`
	Namespace   string `json:"namespace" gorm:"not null" validate:"required,max=100"`
	Project     string `json:"project" gorm:"not null" validate:"required,max=100"`
	Description string `json:"description" validate:"max=500"`
	
	// Git Repository Information
	RepoURL        string `json:"repo_url" gorm:"not null" validate:"required,url"`
	Path           string `json:"path" gorm:"not null" validate:"required"`
	TargetRevision string `json:"target_revision" gorm:"default:HEAD"`
	
	// Destination Information
	DestinationServer    string `json:"destination_server" gorm:"not null" validate:"required"`
	DestinationNamespace string `json:"destination_namespace" gorm:"not null" validate:"required"`
	
	// Sync Policy
	AutoSync     bool   `json:"auto_sync" gorm:"default:false"`
	SyncPolicy   string `json:"sync_policy" gorm:"type:text"`
	
	// Application Status
	Health       string `json:"health"`
	SyncStatus   string `json:"sync_status"`
	LastSyncTime *time.Time `json:"last_sync_time"`
	
	// Metadata
	Labels      string `json:"labels" gorm:"type:text"` // JSON string
	Annotations string `json:"annotations" gorm:"type:text"` // JSON string
	
	// Template Information
	TemplateType string `json:"template_type"` // helm, kustomize, plain, jsonnet
	Values       string `json:"values" gorm:"type:text"` // Helm values or kustomize parameters
	
	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	ClusterID uint    `json:"cluster_id" gorm:"not null"`
	Cluster   Cluster `json:"cluster" gorm:"foreignKey:ClusterID"`
	
	// Application Events and Logs
	Events []ApplicationEvent `json:"events" gorm:"foreignKey:ApplicationID"`
}

// ApplicationEvent represents application deployment events
type ApplicationEvent struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	ApplicationID uint      `json:"application_id" gorm:"not null"`
	EventType     string    `json:"event_type" gorm:"not null"` // sync, health, error
	Message       string    `json:"message" gorm:"type:text"`
	Reason        string    `json:"reason"`
	Component     string    `json:"component"`
	Timestamp     time.Time `json:"timestamp"`
	CreatedAt     time.Time `json:"created_at"`
}

// Repository represents a Git repository
type Repository struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"uniqueIndex;not null" validate:"required,max=100"`
	URL         string `json:"url" gorm:"uniqueIndex;not null" validate:"required,url"`
	Type        string `json:"type" gorm:"not null"` // git, helm
	Description string `json:"description" validate:"max=500"`
	
	// Authentication
	Username       string `json:"username"`
	Password       string `json:"-"` // Encrypted
	SSHPrivateKey  string `json:"-" gorm:"type:text"` // Encrypted
	TLSClientCert  string `json:"-" gorm:"type:text"` // Encrypted
	TLSClientKey   string `json:"-" gorm:"type:text"` // Encrypted
	
	// Repository Status
	ConnectionStatus string    `json:"connection_status"`
	LastConnected    *time.Time `json:"last_connected"`
	
	// Metadata
	Labels      string `json:"labels" gorm:"type:text"` // JSON string
	Annotations string `json:"annotations" gorm:"type:text"` // JSON string
	
	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	Applications []Application `json:"applications" gorm:"foreignKey:RepoURL;references:URL"`
}

// Cluster represents a Kubernetes cluster
type Cluster struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"uniqueIndex;not null" validate:"required,max=100"`
	Server      string `json:"server" gorm:"uniqueIndex;not null" validate:"required,url"`
	Description string `json:"description" validate:"max=500"`
	
	// Cluster Configuration
	Config     string `json:"-" gorm:"type:text"` // Encrypted kubeconfig
	Namespaces string `json:"namespaces" gorm:"type:text"` // JSON array of allowed namespaces
	
	// Cluster Status
	ConnectionStatus string    `json:"connection_status"`
	LastConnected    *time.Time `json:"last_connected"`
	Version          string    `json:"version"`
	
	// Metadata
	Labels      string `json:"labels" gorm:"type:text"` // JSON string
	Annotations string `json:"annotations" gorm:"type:text"` // JSON string
	
	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	Applications []Application `json:"applications" gorm:"foreignKey:ClusterID"`
}

// Project represents an Argo CD project
type Project struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"uniqueIndex;not null" validate:"required,max=100"`
	Description string `json:"description" validate:"max=500"`
	
	// Project Policies
	SourceRepos      string `json:"source_repos" gorm:"type:text"` // JSON array
	Destinations     string `json:"destinations" gorm:"type:text"` // JSON array
	ClusterResources string `json:"cluster_resources" gorm:"type:text"` // JSON array
	Namespaces       string `json:"namespaces" gorm:"type:text"` // JSON array
	
	// RBAC
	Roles string `json:"roles" gorm:"type:text"` // JSON array
	
	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}
