package router

import (
	"gitops-platform/internal/config"
	"gitops-platform/internal/handler"
	"gitops-platform/internal/middleware"
	"gitops-platform/internal/service"
	"gitops-platform/pkg/logger"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func SetupRouter(services *service.Services, cfg *config.Config) *gin.Engine {
	// Set Gin mode
	gin.SetMode(cfg.Server.Mode)

	r := gin.New()

	// Middleware
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORSMiddleware())

	// Initialize handlers
	healthHandler := handler.NewHealthHandler()
	authHandler := handler.NewAuthHandler(services.Auth)
	appHandler := handler.NewApplicationHandler(services.Application)

	// Health check endpoints
	r.GET("/health", healthHandler.Health)
	r.GET("/ready", healthHandler.Ready)

	// Swagger documentation
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Auth routes (no authentication required)
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
			auth.GET("/profile", middleware.AuthMiddleware(services.Auth), authHandler.Profile)
		}

		// Protected routes (authentication required)
		protected := v1.Group("")
		protected.Use(middleware.AuthMiddleware(services.Auth))
		{
			// Application routes
			applications := protected.Group("/applications")
			{
				applications.GET("", 
					middleware.PermissionMiddleware("applications", "read"), 
					appHandler.ListApplications)
				applications.GET("/:id", 
					middleware.PermissionMiddleware("applications", "read"), 
					appHandler.GetApplication)
				applications.POST("", 
					middleware.PermissionMiddleware("applications", "write"), 
					appHandler.CreateApplication)
				applications.PUT("/:id", 
					middleware.PermissionMiddleware("applications", "write"), 
					appHandler.UpdateApplication)
				applications.DELETE("/:id", 
					middleware.PermissionMiddleware("applications", "delete"), 
					appHandler.DeleteApplication)
				applications.POST("/:id/sync", 
					middleware.PermissionMiddleware("applications", "sync"), 
					appHandler.SyncApplication)
				applications.GET("/:id/events", 
					middleware.PermissionMiddleware("applications", "read"), 
					appHandler.GetApplicationEvents)
			}

			// TODO: Add other resource routes (repositories, clusters, projects, users, etc.)
		}
	}

	logger.Info("Router setup completed")
	return r
}
