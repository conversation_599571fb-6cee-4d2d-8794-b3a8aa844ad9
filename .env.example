# GitOps Platform Environment Variables
# Copy this file to .env and update the values

# Server Configuration
SERVER_PORT=8080
SERVER_MODE=debug

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_DBNAME=gitops_platform
DATABASE_SSLMODE=disable

# Argo CD Configuration
ARGOCD_SERVER=localhost:8080
ARGOCD_USERNAME=admin
ARGOCD_PASSWORD=admin
ARGOCD_TOKEN=
ARGOCD_INSECURE=true

# Authentication Configuration
AUTH_JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
AUTH_TOKEN_EXPIRY=3600
AUTH_REFRESH_EXPIRY=86400

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
