package main

import (
	"log"
	"gitops-platform/internal/config"
	"gitops-platform/internal/database"
	"gitops-platform/internal/router"
	"gitops-platform/internal/service"
	"gitops-platform/pkg/logger"
)

// @title GitOps Management Platform API
// @version 1.0
// @description A comprehensive GitOps management platform based on Argo CD
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Initialize configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	logger.Init(cfg.Log.Level)

	// Initialize database
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize services
	services := service.NewServices(db, cfg)

	// Initialize router
	r := router.SetupRouter(services, cfg)

	// Start server
	logger.Info("Starting GitOps Management Platform on port %s", cfg.Server.Port)
	if err := r.Run(":" + cfg.Server.Port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
