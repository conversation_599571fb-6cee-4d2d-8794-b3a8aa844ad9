# GitOps Management Platform

A comprehensive GitOps management platform built with Go Gin framework, providing a unified interface for managing Argo CD applications, repositories, clusters, and projects.

## Features

### 🚀 Core Features
- **Git Repository Integration** - Support for multiple Git repositories (GitHub, GitLab, Bitbucket)
- **Application Management** - Full CRUD operations for Argo CD applications
- **Multi-Cluster Support** - Manage applications across multiple Kubernetes clusters
- **Auto-Sync & Deployment** - Automated synchronization and deployment strategies
- **Real-time Monitoring** - Application status monitoring and health checks
- **RBAC & Security** - Role-based access control with JWT authentication

### 🔧 Technical Features
- **RESTful API** - Comprehensive REST API with Swagger documentation
- **Database Integration** - PostgreSQL with GORM ORM
- **Caching** - Redis for session management and caching
- **Logging** - Structured logging with Logrus
- **Docker Support** - Containerized deployment with Docker Compose
- **Configuration Management** - Flexible configuration with Viper

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   GitOps API    │    │   Argo CD API   │
│                 │◄──►│                 │◄──►│                 │
│  (Web/Mobile)   │    │  (Go Gin)       │    │  (gRPC/REST)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │   Database      │
                       └─────────────────┘
```

## Quick Start

### Prerequisites
- Go 1.21+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+
- Argo CD 2.8+

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd gitops-platform
```

2. **Install dependencies**
```bash
make deps
```

3. **Start services with Docker Compose**
```bash
make up
```

4. **Access the services**
- GitOps API: http://localhost:8080
- Argo CD UI: http://localhost:8081
- API Documentation: http://localhost:8080/swagger/index.html

### Development Setup

1. **Install development tools**
```bash
make install-tools
```

2. **Run in development mode with hot reload**
```bash
make dev
```

3. **Generate Swagger documentation**
```bash
make swagger
```

## API Documentation

### Authentication
All protected endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Default Credentials
- Username: `admin`
- Password: `admin123`

### Key Endpoints

#### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh token
- `GET /api/v1/auth/profile` - Get user profile

#### Applications
- `GET /api/v1/applications` - List applications
- `POST /api/v1/applications` - Create application
- `GET /api/v1/applications/{id}` - Get application
- `PUT /api/v1/applications/{id}` - Update application
- `DELETE /api/v1/applications/{id}` - Delete application
- `POST /api/v1/applications/{id}/sync` - Sync application

## Configuration

Configuration is managed through YAML files and environment variables:

```yaml
server:
  port: "8080"
  mode: "debug"

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "gitops_platform"

argocd:
  server: "localhost:8080"
  username: "admin"
  password: "admin"
  insecure: true

auth:
  jwt_secret: "your-secret-key"
  token_expiry: 3600
```

## Development

### Project Structure
```
├── cmd/                    # Application entrypoints
├── internal/
│   ├── config/            # Configuration management
│   ├── database/          # Database setup and migrations
│   ├── handler/           # HTTP handlers
│   ├── middleware/        # HTTP middleware
│   ├── models/            # Data models
│   ├── router/            # Route definitions
│   └── service/           # Business logic
├── pkg/                   # Shared packages
├── configs/               # Configuration files
├── docs/                  # API documentation
└── scripts/               # Utility scripts
```

### Available Commands
```bash
make build          # Build the application
make test           # Run tests
make test-coverage  # Run tests with coverage
make run            # Run the application
make dev            # Run with hot reload
make docker-build   # Build Docker image
make up             # Start all services
make down           # Stop all services
make lint           # Run linter
make fmt            # Format code
```

## Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
make up

# Or build and run manually
make docker-build
make docker-run
```

### Production Deployment
1. Update configuration for production environment
2. Set secure JWT secret and database credentials
3. Configure TLS/SSL certificates
4. Set up monitoring and logging
5. Configure backup strategies

## Security

- JWT-based authentication
- Role-based access control (RBAC)
- Password hashing with bcrypt
- SQL injection prevention with GORM
- CORS protection
- Rate limiting (recommended for production)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/swagger/index.html`
- Review the configuration examples in `configs/`
