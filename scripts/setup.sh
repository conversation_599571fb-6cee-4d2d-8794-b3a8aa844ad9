#!/bin/bash

# GitOps Platform Setup Script
# This script helps set up the development environment

set -e

echo "🚀 GitOps Platform Setup"
echo "========================"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21+ first."
    exit 1
fi

# Check Go version
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
REQUIRED_VERSION="1.21"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Go version $GO_VERSION is too old. Please upgrade to Go 1.21+."
    exit 1
fi

echo "✅ Go version $GO_VERSION is compatible"

# Set Go proxy for better connectivity
echo "🔧 Setting up Go proxy..."
export GOPROXY=https://proxy.golang.org,direct
export GOSUMDB=sum.golang.org

# Alternative proxy for China users
# export GOPROXY=https://goproxy.cn,direct

echo "📦 Downloading dependencies..."
go mod download

echo "🔨 Building the application..."
go build -o bin/gitops-platform cmd/main.go

echo "✅ Build completed successfully!"

# Check if Docker is installed
if command -v docker &> /dev/null; then
    echo "🐳 Docker is available"
    
    # Check if Docker Compose is installed
    if command -v docker-compose &> /dev/null; then
        echo "🐳 Docker Compose is available"
        echo ""
        echo "You can now start the services with:"
        echo "  make up"
        echo "  or"
        echo "  docker-compose up -d"
    else
        echo "⚠️  Docker Compose is not installed"
    fi
else
    echo "⚠️  Docker is not installed"
fi

echo ""
echo "🎉 Setup completed!"
echo ""
echo "Next steps:"
echo "1. Configure your settings in configs/config.yaml"
echo "2. Start PostgreSQL and Redis (or use Docker Compose)"
echo "3. Run the application:"
echo "   ./bin/gitops-platform"
echo ""
echo "For development with hot reload:"
echo "   make dev"
echo ""
echo "API Documentation will be available at:"
echo "   http://localhost:8080/swagger/index.html"
