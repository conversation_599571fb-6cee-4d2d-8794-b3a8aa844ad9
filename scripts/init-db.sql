-- Initialize GitOps Platform Database
-- This script creates the initial database and user if they don't exist

-- Create database
CREATE DATABASE IF NOT EXISTS gitops_platform;

-- Create user (if using MySQL)
-- CREATE USER IF NOT EXISTS 'gitops_user'@'%' IDENTIFIED BY 'gitops_password';
-- GRANT ALL PRIVILEGES ON gitops_platform.* TO 'gitops_user'@'%';

-- For PostgreSQL, the database and user are typically created via environment variables
-- in Docker Compose or during container initialization

-- Create extensions (PostgreSQL specific)
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Note: The actual table creation is handled by GORM AutoMigrate in the application
