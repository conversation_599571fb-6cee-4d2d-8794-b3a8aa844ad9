package logger

import (
	"os"
	"github.com/sirupsen/logrus"
)

var log *logrus.Logger

func Init(level string) {
	log = logrus.New()
	
	// Set log level
	switch level {
	case "debug":
		log.SetLevel(logrus.DebugLevel)
	case "info":
		log.SetLevel(logrus.InfoLevel)
	case "warn":
		log.SetLevel(logrus.WarnLevel)
	case "error":
		log.SetLevel(logrus.ErrorLevel)
	default:
		log.SetLevel(logrus.InfoLevel)
	}

	// Set output
	log.SetOutput(os.Stdout)

	// Set formatter
	log.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})
}

func GetLevel() string {
	if log == nil {
		return "info"
	}
	return log.GetLevel().String()
}

func Debug(format string, args ...interface{}) {
	if log != nil {
		log.Debugf(format, args...)
	}
}

func Info(format string, args ...interface{}) {
	if log != nil {
		log.Infof(format, args...)
	}
}

func Warn(format string, args ...interface{}) {
	if log != nil {
		log.Warnf(format, args...)
	}
}

func Error(format string, args ...interface{}) {
	if log != nil {
		log.Errorf(format, args...)
	}
}

func Fatal(format string, args ...interface{}) {
	if log != nil {
		log.Fatalf(format, args...)
	}
}
