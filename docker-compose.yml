version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: gitops-postgres
    environment:
      POSTGRES_DB: gitops_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - gitops-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: gitops-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - gitops-network

  # GitOps Platform API
  gitops-api:
    build: .
    container_name: gitops-api
    ports:
      - "8080:8080"
    environment:
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - DATABASE_DBNAME=gitops_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ARGOCD_SERVER=argocd-server:80
      - ARGOCD_USERNAME=admin
      - ARGOCD_PASSWORD=admin
      - ARGOCD_INSECURE=true
    depends_on:
      - postgres
      - redis
    networks:
      - gitops-network
    volumes:
      - ./configs:/root/configs

  # Argo CD Server (for development/testing)
  argocd-server:
    image: argoproj/argocd:v2.8.4
    container_name: argocd-server
    command: [argocd-server, --insecure]
    ports:
      - "8081:8080"
    environment:
      - ARGOCD_SERVER_INSECURE=true
    networks:
      - gitops-network
    volumes:
      - argocd_data:/home/<USER>

  # Argo CD Repo Server
  argocd-repo-server:
    image: argoproj/argocd:v2.8.4
    container_name: argocd-repo-server
    command: [argocd-repo-server]
    networks:
      - gitops-network
    volumes:
      - argocd_data:/home/<USER>

  # Argo CD Application Controller
  argocd-application-controller:
    image: argoproj/argocd:v2.8.4
    container_name: argocd-application-controller
    command: [argocd-application-controller]
    networks:
      - gitops-network
    volumes:
      - argocd_data:/home/<USER>

volumes:
  postgres_data:
  redis_data:
  argocd_data:

networks:
  gitops-network:
    driver: bridge
