# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
main

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Docker
.dockerignore

# Air (hot reload tool)
.air.toml
tmp/

# Swagger generated files
docs/docs.go
docs/swagger.json
docs/swagger.yaml
