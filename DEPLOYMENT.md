# GitOps 管理平台部署指南

## 项目概述

这是一个基于 Go Gin 框架构建的 GitOps 管理平台，提供了完整的 Argo CD 管理功能，包括：

### 🎯 核心功能
- **Git 仓库集成** - 支持多个 Git 仓库管理（GitHub、GitLab、Bitbucket）
- **应用管理** - 完整的 Argo CD 应用 CRUD 操作
- **多集群支持** - 跨多个 Kubernetes 集群部署应用
- **自动同步部署** - 自动检测变更并同步部署
- **实时状态监控** - 应用健康状态和同步状态监控
- **RBAC 权限控制** - 基于角色的访问控制
- **RESTful API** - 完整的 REST API 和 Swagger 文档

### 🏗️ 技术架构
- **后端框架**: Go 1.21 + Gin
- **数据库**: PostgreSQL 15+ (GORM ORM)
- **缓存**: Redis 7+
- **认证**: JWT + RBAC
- **API 文档**: Swagger/OpenAPI
- **容器化**: Docker + Docker Compose
- **日志**: 结构化日志 (Logrus)

## 快速开始

### 1. 环境要求
- Go 1.21+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose (可选)
- Argo CD 2.8+ (目标管理系统)

### 2. 项目结构
```
gitops-platform/
├── cmd/                    # 应用入口
├── internal/
│   ├── config/            # 配置管理
│   ├── database/          # 数据库初始化
│   ├── handler/           # HTTP 处理器
│   ├── middleware/        # 中间件
│   ├── models/            # 数据模型
│   ├── router/            # 路由定义
│   └── service/           # 业务逻辑
├── pkg/                   # 共享包
├── configs/               # 配置文件
├── scripts/               # 工具脚本
└── docs/                  # 文档
```

### 3. 安装部署

#### 方式一：Docker Compose (推荐)
```bash
# 1. 克隆项目
git clone <repository-url>
cd gitops-platform

# 2. 启动所有服务
make up
# 或者
docker-compose up -d

# 3. 查看日志
make logs
```

#### 方式二：本地开发
```bash
# 1. 运行安装脚本
./scripts/setup.sh

# 2. 配置数据库和 Redis
# 编辑 configs/config.yaml

# 3. 启动应用
make run
# 或者开发模式（热重载）
make dev
```

### 4. 配置说明

编辑 `configs/config.yaml`:

```yaml
server:
  port: "8080"
  mode: "debug"  # debug, release

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "gitops_platform"
  sslmode: "disable"

argocd:
  server: "localhost:8080"      # Argo CD 服务器地址
  username: "admin"             # Argo CD 用户名
  password: "admin"             # Argo CD 密码
  token: ""                     # 或使用 Token 认证
  insecure: true               # 开发环境可设为 true

auth:
  jwt_secret: "your-super-secret-jwt-key"
  token_expiry: 3600           # 1小时
  refresh_expiry: 86400        # 24小时

log:
  level: "info"                # debug, info, warn, error
  format: "json"               # json, text
```

### 5. 服务访问

启动后可访问以下服务：

- **GitOps API**: http://localhost:8080
- **API 文档**: http://localhost:8080/swagger/index.html
- **Argo CD UI**: http://localhost:8081 (Docker Compose 模式)

### 6. 默认账户

系统会自动创建默认管理员账户：
- 用户名: `admin`
- 密码: `admin123`

## API 使用指南

### 认证
所有受保护的 API 都需要 JWT 认证：

```bash
# 1. 登录获取 Token
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 2. 使用 Token 访问 API
curl -X GET http://localhost:8080/api/v1/applications \
  -H "Authorization: Bearer <your-jwt-token>"
```

### 主要 API 端点

#### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新 Token
- `GET /api/v1/auth/profile` - 获取用户信息

#### 应用管理
- `GET /api/v1/applications` - 获取应用列表
- `POST /api/v1/applications` - 创建应用
- `GET /api/v1/applications/{id}` - 获取应用详情
- `PUT /api/v1/applications/{id}` - 更新应用
- `DELETE /api/v1/applications/{id}` - 删除应用
- `POST /api/v1/applications/{id}/sync` - 同步应用

## 开发指南

### 常用命令
```bash
# 构建项目
make build

# 运行测试
make test

# 代码格式化
make fmt

# 代码检查
make lint

# 生成 API 文档
make swagger

# 热重载开发
make dev
```

### 添加新功能

1. **添加数据模型** - 在 `internal/models/` 中定义
2. **创建服务层** - 在 `internal/service/` 中实现业务逻辑
3. **添加处理器** - 在 `internal/handler/` 中处理 HTTP 请求
4. **注册路由** - 在 `internal/router/` 中添加路由
5. **更新权限** - 在数据库初始化中添加相应权限

### 数据库迁移

系统使用 GORM 的 AutoMigrate 功能自动处理数据库结构变更。

## 生产部署

### 安全配置
1. **更改默认密码** - 修改默认管理员密码
2. **设置强 JWT 密钥** - 使用复杂的 JWT 密钥
3. **启用 HTTPS** - 配置 TLS 证书
4. **数据库安全** - 使用强密码和 SSL 连接
5. **网络安全** - 配置防火墙和网络策略

### 性能优化
1. **数据库连接池** - 配置合适的连接池大小
2. **Redis 缓存** - 启用缓存提高性能
3. **日志级别** - 生产环境使用 `info` 或 `warn` 级别
4. **资源限制** - 设置容器资源限制

### 监控告警
1. **健康检查** - 使用 `/health` 端点
2. **日志监控** - 集成 ELK 或其他日志系统
3. **指标监控** - 集成 Prometheus + Grafana
4. **告警通知** - 配置 Slack、邮件等通知

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接配置是否正确

2. **Argo CD 连接失败**
   - 确认 Argo CD 服务地址
   - 检查认证信息是否正确

3. **权限不足**
   - 检查用户角色和权限配置
   - 确认 JWT Token 是否有效

4. **应用同步失败**
   - 检查 Git 仓库访问权限
   - 验证 Kubernetes 集群连接

### 日志查看
```bash
# Docker Compose 模式
docker-compose logs -f gitops-api

# 本地运行模式
tail -f logs/app.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 添加测试用例
5. 提交 Pull Request

## 许可证

MIT License - 详见 LICENSE 文件
